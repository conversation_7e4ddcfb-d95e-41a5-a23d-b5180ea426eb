package org.jeecg.modules.cw.ds.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.constants.CwCllDataName;
import org.jeecg.modules.cw.base.entity.CwCllCblData;
import org.jeecg.modules.cw.base.entity.CwCllData;
import org.jeecg.modules.cw.base.entity.CwCllycData;
import org.jeecg.modules.cw.base.entity.CwDwBase;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwCllCblDataService;
import org.jeecg.modules.cw.base.service.ICwCllDataService;
import org.jeecg.modules.cw.base.service.ICwCllycDataService;
import org.jeecg.modules.cw.base.service.ICwDwBaseService;
import org.jeecg.modules.cw.base.service.ICwNameDictService;

import org.jeecg.modules.cw.ckc.entity.CwCkcDay;
import org.jeecg.modules.cw.ds.entity.CwDsDay;
import org.jeecg.modules.cw.ds.entity.CwDsMonth;
import org.jeecg.modules.cw.ds.entity.CwDsRow;
import org.jeecg.modules.cw.ds.mapper.CwDsDayMapper;
import org.jeecg.modules.cw.ds.param.CwDsZhcbSumbitParam;
import org.jeecg.modules.cw.ds.result.CwDsZhcbListResult;
import org.jeecg.modules.cw.ds.service.ICwDsDayService;
import org.jeecg.modules.cw.ds.service.ICwDsMonthService;
import org.jeecg.modules.cw.ds.service.ICwDsZhcbService;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;
import org.jeecg.modules.cw.common.ForecastChangeDetector;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import javax.annotation.PreDestroy;
import java.util.stream.Collectors;

/**
 * @Description: 大山厂-日填报
 * @Author: jeecg-boot
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Service
@Slf4j
public class CwDsZhcbServiceImpl implements ICwDsZhcbService {

    private static final String DICT_TYPE = "ds";
    private static final String CACHE_KEY = "cw:ds:zhcb:";
    private static final String QUERY_CACHE_KEY = CACHE_KEY + "query";
    private static final String SUM_DRS_CACHE_KEY = CACHE_KEY + "sumDrs";
    private static final String SUM_BY_MONTH_CACHE_KEY = CACHE_KEY + "sumByMonth";
    private static final String NAME_DS = "ds";

    @Resource
    private ICwDwBaseService dwBaseService;
    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwDsDayService dsDayService;
    @Resource
    private ICwDsMonthService dsMonthService;
    @Resource
    private CwDsDayMapper dsDayMapper;
    @Resource
    private ICwCllCblDataService cllCblDataService;
    @Resource
    private ICwCllDataService cwCllDataService;
    @Resource
    private ICwCllycDataService cwCllycDataService;

    @Lazy
    @Resource
    private ICwMnlrStatisticsDayService mnlrStatisticsDayService;

    private final ExecutorService drsRecalcExecutor = Executors.newSingleThreadExecutor(new ThreadFactory() {
        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, "ds-drs-recalc");
            t.setDaemon(true);
            return t;
        }
    });

    @PreDestroy
    public void shutdown() { drsRecalcExecutor.shutdown(); }

    @Override
    public CwDsZhcbListResult query(Date queryDate) {
        // 1. 初始化结果对象
        CwDsZhcbListResult result = new CwDsZhcbListResult();
        result.setQueryDate(queryDate);
        
        // 2. 获取基础数据
        // 2.1 日期相关
        Date beginOfMonth = DateUtil.beginOfMonth(queryDate);
        Date endOfDay = DateUtil.endOfDay(queryDate);
        Date dayBeforeQuery = DateUtil.offsetDay(queryDate, -1);
        int currentDay = DateUtil.dayOfMonth(queryDate);
        boolean isFirstHalf = currentDay <= 15;
        
        // 2.2 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);

        // 2.2.1 获取当月处理量预测(cllyc)，用于缺失日数据补全
        BigDecimal monthCllyc = null;
        {
            Date monthBegin = DateUtil.beginOfMonth(queryDate);
            CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                    .eq(CwCllycData::getRecordTime, monthBegin)
                    .eq(CwCllycData::getName, NAME_DS)
                    .one();
            if (ObjectUtil.isNotEmpty(cllycData)) {
                monthCllyc = cllycData.getCllyc();
            }
        }
        
        // 2.3 日表和月表数据
        List<CwDsDay> allDaysInMonth = dsDayService.lambdaQuery()
                .between(CwDsDay::getRecordTime, beginOfMonth, endOfDay)
                .list();
        List<CwDsMonth> months = dsMonthService.lambdaQuery()
                .ge(CwDsMonth::getRecordTime, beginOfMonth)
                .le(CwDsMonth::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();

        // === 自动补全缺失日数据 ===
        if (ObjectUtil.isNotEmpty(monthCllyc) && monthCllyc.compareTo(BigDecimal.ZERO) > 0) {
            List<CwDsDay> filled = autoFillMissingDays(beginOfMonth, queryDate, dict, monthCllyc, months);
            if (ObjectUtil.isNotEmpty(filled)) {
                allDaysInMonth.addAll(filled);
            }
        }
        // 将日数据按名称分组
        Map<String, List<CwDsDay>> daysByName = allDaysInMonth.stream().collect(Collectors.groupingBy(CwDsDay::getName));
        
        // 3. 构建结果行数据
        List<CwDsRow> resRows = new ArrayList<>();
        
        for (CwNameDict d : dict) {
            CwDsRow row = new CwDsRow();
            BeanUtil.copyProperties(d, row);
            
            // 3.1 填充月数据
            months.stream()
                  .filter(m -> d.getName().equals(m.getName()))
                  .findFirst()
                  .ifPresent(m -> {
                      // 设置平均单价和月预算
                      row.setPjdj(m.getPjdj());
                      row.setYys(m.getYys());
                      
                      // 根据当前是上半月还是下半月，设置月预测显示
                      if (!isFirstHalf && m.getSecondHalfForecast() != null) {
                          row.setYyc(m.getSecondHalfForecast());
                      }
                  });
            
            List<CwDsDay> itemDays = daysByName.get(d.getName());
            if (ObjectUtil.isNotEmpty(itemDays)) {
                // 3.2 填充当日日数据和当日数
                itemDays.stream()
                         .filter(day -> DateUtil.isSameDay(queryDate, day.getRecordTime()))
                         .findFirst()
                         .ifPresent(day -> {
                             row.setPjzh(day.getPjzh());
                             row.setRemark(day.getRemark());
                             if (day.getDrs() != null) {
                                 row.setDrs(day.getDrs());
                             }
                         });

                // 3.4 计算月累计 (直接累加drs)
                BigDecimal ylj = itemDays.stream()
                        .filter(day -> !day.getRecordTime().after(dayBeforeQuery))
                        .map(CwDsDay::getDrs)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                row.setYlj(ylj);
            }
            
            resRows.add(row);
        }
        
        result.setRows(resRows);
        
        // 4. 设置额外数据
        // 4.1 从CllData获取大山厂处理量
        Map<String, String> dsCllDataMap = cwCllDataService.getRangeCllData(CwCllDataName.DSCLL, queryDate, queryDate);
        if(ObjectUtil.isNotEmpty(dsCllDataMap)){
            String todayKey = DateUtil.format(queryDate, "yyyy-MM-dd");
            String dsCllStr = dsCllDataMap.get(todayKey);
            if(ObjectUtil.isNotEmpty(dsCllStr)){
                // 数据库存储单位为万吨，前端展示单位为万吨
                result.setDsCll(new BigDecimal(dsCllStr));
            }
        }
        
        // 4.3 处理量预测
        CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, beginOfMonth)
                .eq(CwCllycData::getName, NAME_DS)
                .one();
        if (ObjectUtil.isNotEmpty(cllycData) && ObjectUtil.isNotEmpty(cllycData.getCllyc())) {
            result.setCllyc(cllycData.getCllyc());
        }
        
        return result;
    }

    @Override
    public void submit(CwDsZhcbSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 标识 cllyc 是否有变动（新增、修改或删除）
        boolean cllycChanged = false;
        // 月初日期，用于 cllyc 记录及整月重算
        Date monthBegin = DateUtil.beginOfMonth(submitDate);
        
        // 更新大山厂处理量数据到cw_cll_data表
        if (param.getBase().getDsCll() != null) {
            cwCllDataService.setCllData(CwCllDataName.DSCLL, param.getBase().getDsCll().stripTrailingZeros().toPlainString(), submitDate);
        }
        
        // ========== 处理量预测 (cllyc) 的增、改、删 ==========
        BigDecimal newCllyc = param.getBase() != null ? param.getBase().getCllyc() : null;
        CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, monthBegin)
                .eq(CwCllycData::getName, NAME_DS)
                .one();

        if (newCllyc != null) {
            // 新值不为空 → 新增或修改
            if (cllycData == null) {
                cllycData = new CwCllycData();
                cllycData.setRecordTime(monthBegin);
                cllycData.setName(NAME_DS);
                cllycChanged = true;
            } else if (cllycData.getCllyc() == null || cllycData.getCllyc().compareTo(newCllyc) != 0) {
                cllycChanged = true;
            }

            cllycData.setCllyc(newCllyc);
            if (cllycData.getId() == null) {
                cwCllycDataService.save(cllycData);
            } else {
                cwCllycDataService.updateById(cllycData);
            }
        } else {
            // 新值为空 → 删除
            if (cllycData != null) {
                cwCllycDataService.removeById(cllycData.getId());
                cllycChanged = true;
            }
        }
        
        // 更新base
        Long baseCount = dwBaseService.lambdaQuery()
                .eq(CwDwBase::getRecordTime, submitDate)
                .count();
        if (baseCount == 0) {
            CwDwBase base = new CwDwBase();
            base.setRecordTime(submitDate);
            base.setDsCll(param.getBase().getDsCll());
            dwBaseService.save(base);
        } else {
            dwBaseService.lambdaUpdate()
                    .set(CwDwBase::getDsCll, param.getBase().getDsCll())
                    .eq(CwDwBase::getRecordTime, submitDate)
                    .update();
        }
        
        // 更新日数据
        List<CwDsRow> rows = param.getRows();
        dsDayService.remove(new LambdaQueryWrapper<>(CwDsDay.class).eq(CwDsDay::getRecordTime, submitDate));
        
        // 获取已有的月数据
        List<CwDsMonth> existingMonths = dsMonthService.lambdaQuery()
                .ge(CwDsMonth::getRecordTime, DateUtil.beginOfMonth(submitDate))
                .le(CwDsMonth::getRecordTime, DateUtil.endOfMonth(submitDate))
                .list();
        Map<String, CwDsMonth> existingMonthMap = new HashMap<>();
        for (CwDsMonth month : existingMonths) {
            existingMonthMap.put(month.getName(), month);
        }
        
        ArrayList<CwDsDay> days = new ArrayList<>();
        for (CwDsRow row : rows) {
            CwDsDay day = new CwDsDay();
            BeanUtil.copyProperties(row, day);
            day.setRecordTime(submitDate);
            day.setDrs(BigDecimal.ZERO);
            days.add(day);
        }
        dsDayService.saveBatch(days);
        
        ArrayList<CwDsMonth> monthsToSave = new ArrayList<>();
        Boolean isFirstHalf = param.getIsFirstHalf();
        
        for (CwDsRow row : rows) {
            CwDsMonth month;
            if (existingMonthMap.containsKey(row.getName())) {
                // 使用已存在的月数据
                month = existingMonthMap.get(row.getName());
                
                // 只更新当前半月的预测值，保留另一半月的预测值
                if (ObjectUtil.isNotEmpty(row.getYyc())) {
                    BigDecimal forecastValue = row.getYyc();
                    if (isFirstHalf != null) {
                        if (isFirstHalf) {
                            month.setFirstHalfForecast(forecastValue);
                        } else {
                            month.setSecondHalfForecast(forecastValue);
                        }
                    }
                }
                
                // 更新其他字段
                if (ObjectUtil.isNotEmpty(row.getPjdj())) {
                    month.setPjdj(row.getPjdj());
                }
                if (ObjectUtil.isNotEmpty(row.getYys())) {
                    month.setYys(row.getYys());
                }
            } else {
                // 创建新的月数据
                month = new CwDsMonth();
                BeanUtil.copyProperties(row, month);
                month.setRecordTime(submitDate);
                
                // 根据isFirstHalf设置上半月或下半月预测值
                if (ObjectUtil.isNotEmpty(row.getYyc())) {
                    BigDecimal forecastValue = row.getYyc();
                    if (isFirstHalf != null) {
                        if (isFirstHalf) {
                            month.setFirstHalfForecast(forecastValue);
                        } else {
                            month.setSecondHalfForecast(forecastValue);
                        }
                    }
                }
            }
            monthsToSave.add(month);
        }
        
        // 批量保存或更新月数据
        dsMonthService.saveOrUpdateBatch(monthsToSave);

        // 根据 cllyc 是否变更决定重算范围
        if (cllycChanged) {
            this.recalculateDrs(monthBegin, DateUtil.endOfMonth(submitDate));
        } else {
            this.recalculateDrsByDate(submitDate);
        }
    }

    @Override
    public void recalculateDrsByDate(Date date) {
        log.info("开始重新计算大山厂{}的drs", DateUtil.formatDate(date));
        recalculateDrs(date, date);
        log.info("结束重新计算大山厂{}的drs", DateUtil.formatDate(date));
    }

    @Override
    public void recalculateAllDrs() {
        log.info("开始重新计算大山所有的drs");
        // 获取所有有记录的日期
        List<Date> dates = dsDayService.list().stream()
                .map(CwDsDay::getRecordTime)
                .map(DateUtil::beginOfDay)
                .distinct()
                .collect(Collectors.toList());

        // 逐日重新计算
        for (Date date : dates) {
            try {
                recalculateDrsByDate(date);
            } catch (Exception e) {
                log.error("重新计算大山drs失败，日期: {}", DateUtil.formatDate(date), e);
            }
        }
        log.info("结束重新计算大山所有的drs");
    }

    @Override
    public void recalculateTodayDrs() {
        recalculateDrsByDate(new Date());
    }

    /**
     * 重新计算指定日期范围内的所有大山每日drs
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    private void recalculateDrs(Date startDate, Date endDate) {
        // 1. 查询日期范围内的所有日数据
        List<CwDsDay> allDaysInRange = dsDayService.lambdaQuery()
                .between(CwDsDay::getRecordTime, DateUtil.beginOfDay(startDate), DateUtil.endOfDay(endDate))
                .list();

        if (allDaysInRange.isEmpty()) {
            return;
        }

        // 2. 按月份对数据进行分组
        Map<String, List<CwDsDay>> daysByMonth = allDaysInRange.stream()
                .collect(Collectors.groupingBy(d -> DateUtil.format(d.getRecordTime(), "yyyy-MM")));

        List<CwDsDay> daysToUpdate = new ArrayList<>();

        // 3. 按月份处理数据
        for (Map.Entry<String, List<CwDsDay>> entry : daysByMonth.entrySet()) {
            String monthKey = entry.getKey();
            List<CwDsDay> daysInMonth = entry.getValue();
            Date monthDate = DateUtil.parse(monthKey, "yyyy-MM");
            Date beginOfMonth = DateUtil.beginOfMonth(monthDate);
            Date endOfMonth = DateUtil.endOfMonth(monthDate);

            // 3.1 获取月表数据
            List<CwDsMonth> months = dsMonthService.lambdaQuery()
                    .ge(CwDsMonth::getRecordTime, beginOfMonth)
                    .le(CwDsMonth::getRecordTime, endOfMonth)
                    .list();

            // 3.2 获取处理量预测数据
            Map<String, BigDecimal> cllycDataMap = getMonthCllycData(monthDate);
            BigDecimal cllyc = cllycDataMap.get(monthKey);

            // 3.3 获取大山处理量数据
            Map<String, BigDecimal> dsCllDataMap = getRangeDsCllData(beginOfMonth, endOfMonth);

            // 4. 遍历当月的每一天数据，计算drs
            for (CwDsDay day : daysInMonth) {
                String dateKey = DateUtil.format(day.getRecordTime(), "yyyy-MM-dd");
                BigDecimal dsCll = dsCllDataMap.get(dateKey);

                BigDecimal drs = calculateDrs(day, cllyc, dsCll, months);

                day.setDrs(drs);
                daysToUpdate.add(day);
            }
        }

        // 5. 批量更新
        if (!daysToUpdate.isEmpty()) {
            dsDayService.updateBatchById(daysToUpdate);
        }

        // 同步重算模拟利润（日）
        mnlrStatisticsDayService.recalcRange(startDate, endDate);
    }

    private BigDecimal calculateDrs(CwDsDay day, BigDecimal cllyc, BigDecimal dsCll, List<CwDsMonth> months) {
            String name = day.getName();
            Date recordTime = day.getRecordTime();
            BigDecimal yyc = null;
            BigDecimal pjdj = null;

            // 检查是否为月末最后一天
            boolean isLastDayOfMonth = DateUtil.isSameDay(recordTime, DateUtil.endOfMonth(recordTime));

            for (CwDsMonth month : months) {
                if (name.equals(month.getName())) {
                    pjdj = month.getPjdj();

                    // 最高优先级：月末预测（仅影响月末最后一天）
                    if (isLastDayOfMonth && month.getMonthEndForecast() != null) {
                        yyc = month.getMonthEndForecast();
                    }
                    // 第二优先级：下半月预测数据（影响整个月）
                    else if (month.getSecondHalfForecast() != null) {
                        yyc = month.getSecondHalfForecast();
                    }
                    // 第三优先级：根据日期使用月预算或下半月预测
                    else {
                        int recordDay = DateUtil.dayOfMonth(recordTime);
                        boolean recordIsFirstHalf = recordDay <= 15;

                        if (recordIsFirstHalf) {
                            // 上半月使用月预算
                            if (month.getYys() != null) {
                                yyc = month.getYys();
                            }
                        } else {
                            // 下半月使用下半月预测（但此时应该为null，因为上面已经检查过）
                            if (month.getSecondHalfForecast() != null) {
                                yyc = month.getSecondHalfForecast();
                            }
                        }
                    }
                    break;
                }
            }
    
            // 只有当月预算/预测、处理量预测和大山处理量都有值时，才计算当日数
            if (ObjectUtil.isNotEmpty(cllyc) && ObjectUtil.isNotEmpty(yyc) &&
                    cllyc.compareTo(BigDecimal.ZERO) > 0 && ObjectUtil.isNotEmpty(dsCll)) {
                // 单位成本 = 月预算或月预测 / 处理量预测
                BigDecimal dwcb = yyc.divide(cllyc, 10, BigDecimal.ROUND_HALF_UP);
                // 当日数 = 单位成本 * 大山处理量
                return dwcb.multiply(dsCll);
            }
    
            BigDecimal pjzh = day.getPjzh();
            if (ObjectUtil.isNotEmpty(pjzh) && ObjectUtil.isNotEmpty(pjdj)) {
                return pjzh.multiply(pjdj);
            }
    
            return BigDecimal.ZERO;
        }

    /**
     * 获取指定月份内所有日期的处理量预测数据
     * @param monthDate 月份日期
     * @return 处理量预测数据
     */
    private Map<String, BigDecimal> getMonthCllycData(Date monthDate) {
        Map<String, BigDecimal> result = new HashMap<>();
        // 获取月初日期
        Date beginOfMonth = DateUtil.beginOfMonth(monthDate);
        String monthKey = DateUtil.format(beginOfMonth, "yyyy-MM");
        
        // 查询处理量预测
        CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, beginOfMonth)
                .eq(CwCllycData::getName, NAME_DS)
                .one();
        
        if (ObjectUtil.isNotEmpty(cllycData) && ObjectUtil.isNotEmpty(cllycData.getCllyc())) {
            result.put(monthKey, cllycData.getCllyc());
        }
        
        return result;
    }
    
    /**
     * 获取指定日期范围内所有日期的采拨总量数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 采拨总量数据映射，key为日期字符串(yyyy-MM-dd)，value为采拨总量
     */
    private Map<String, BigDecimal> getRangeCbzlData(Date startDate, Date endDate) {
        // 使用CwCllData服务获取日期范围内的大山厂处理量数据
        Map<String, String> dataMap = cwCllDataService.getRangeCllData(CwCllDataName.DSCLL, startDate, endDate);
        
        // 转换为BigDecimal
        Map<String, BigDecimal> result = new HashMap<>();
        if (ObjectUtil.isNotEmpty(dataMap)) {
            dataMap.forEach((dateStr, dataStr) -> {
                if (ObjectUtil.isNotEmpty(dataStr)) {
                    result.put(dateStr, new BigDecimal(dataStr));
                }
            });
        }
        
        return result;
    }

    /**
     * 获取指定日期范围内所有日期的大山厂处理量数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 大山厂处理量数据映射，key为日期字符串(yyyy-MM-dd)，value为采剥总量
     */
    private Map<String, BigDecimal> getRangeDsCllData(Date startDate, Date endDate) {
        // 使用CwCllData服务获取日期范围内的采剥总量数据
        Map<String, String> dataMap = cwCllDataService.getRangeCllData(CwCllDataName.DSCLL, startDate, endDate);

        // 转换为BigDecimal
        Map<String, BigDecimal> result = new HashMap<>();
        if (ObjectUtil.isNotEmpty(dataMap)) {
            dataMap.forEach((dateStr, dataStr) -> {
                if (ObjectUtil.isNotEmpty(dataStr)) {
                    result.put(dateStr, new BigDecimal(dataStr));
                }
            });
        }

        return result;
    }

    /**
     * 计算月累计
     * @param name 项目名称
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param months 月数据列表
     * @param cllycDataMap 处理量预测数据映射
     * @param cbzlDataMap 采剥总量数据映射
     * @return 月累计值
     */
    private BigDecimal calculateMonthlyAccumulation(
            String name, 
            Date startDate, 
            Date endDate, 
            List<CwDsMonth> months,
            Map<String, BigDecimal> cllycDataMap,
            Map<String, BigDecimal> cbzlDataMap) {
        
        BigDecimal sum = BigDecimal.ZERO;
        
        // 如果开始日期晚于结束日期，直接返回0
        if (startDate.compareTo(endDate) > 0) {
            return sum;
        }
        
        // 获取指定日期范围内的所有日期（包括开始日期和结束日期）
        List<Date> dateRange = new ArrayList<>();
        Date currentDate = startDate;
        while (!currentDate.after(endDate)) {
            dateRange.add(currentDate);
            currentDate = DateUtil.offsetDay(currentDate, 1);
        }
        
        // 为每个日期计算当日数并累计
        for (Date date : dateRange) {
            // 获取处理量预测
            String dateMonthKey = DateUtil.format(DateUtil.beginOfMonth(date), "yyyy-MM");
            BigDecimal dateCllyc = cllycDataMap.get(dateMonthKey);
            
            // 获取采剥总量
            String dateKey = DateUtil.format(date, "yyyy-MM-dd");
            BigDecimal dateCbzl = cbzlDataMap.get(dateKey);
            
            // 计算当日数
//            BigDecimal dateDrs = calculateDrs(name, date, dateCllyc, dateCbzl, months);

            // 累加当日数
//            if (ObjectUtil.isNotEmpty(dateDrs)) {
//                sum = sum.add(dateDrs);
//            }
        }
        
        return sum;
    }

    @Override
    public BigDecimal sumDrs(Date queryDate) {
        // 1. 日期相关
        Date beginOfMonth = DateUtil.beginOfMonth(queryDate);
        Date endOfMonth = DateUtil.endOfMonth(queryDate);

        // 2. 数据准备
        List<CwDsDay> allDays = dsDayService.lambdaQuery()
                .between(CwDsDay::getRecordTime, beginOfMonth, endOfMonth)
                .list();

        // 3. 直接累加drs
        return allDays.stream()
                .map(CwDsDay::getDrs)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<CwKrbRow> sumByMonth(Date queryDate) {
        // 1. 初始化结果
        Map<String, CwKrbRow> result = CwKrbRow.takeDefaultKrbRows("大山厂");
        
        // 2. 获取基础数据
        // 2.1 日期相关
        Date beginOfMonth = DateUtil.beginOfMonth(queryDate);
        Date endOfDay = DateUtil.endOfDay(queryDate);
        
        // 2.2 数据准备
        List<CwDsDay> allDays = dsDayService.lambdaQuery()
                .between(CwDsDay::getRecordTime, beginOfMonth, endOfDay)
                .list();
        List<CwDsMonth> months = dsMonthService.lambdaQuery()
                .ge(CwDsMonth::getRecordTime, beginOfMonth)
                .le(CwDsMonth::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();
        
        // 3. 定义计算分类行
        CwKrbRow clRow = result.get(CwKrbRow.CL);
        CwKrbRow bjRow = result.get(CwKrbRow.BJ);
        CwKrbRow rlRow = result.get(CwKrbRow.RL);
        CwKrbRow sRow = result.get(CwKrbRow.S);
        CwKrbRow dRow = result.get(CwKrbRow.D);
        CwKrbRow zzfyRow = result.get(CwKrbRow.ZZFY);
        
        // 4. 处理每天的数据
        for (CwDsDay day : allDays) {
            // 直接获取drs值
            BigDecimal calculatedValue = day.getDrs();
            
            // 如果计算出了值，则进行累加
            if (ObjectUtil.isNotEmpty(calculatedValue)) {
                // 只有当天的数据才计入当日数
                if (DateUtil.isSameDay(queryDate, day.getRecordTime())) {
                    addToDailyTotal(day.getType(), calculatedValue, clRow, bjRow, rlRow, sRow, dRow, zzfyRow);
                }
                
                // 所有当月数据都计入月累计
                addToMonthlyTotal(day.getType(), calculatedValue, clRow, bjRow, rlRow, sRow, dRow, zzfyRow);
            }
        }
        
        // 5. 处理月预算数据
        for (CwDsMonth month : months) {
            BigDecimal yys = month.getYys();
            if (ObjectUtil.isNotEmpty(month) && ObjectUtil.isNotEmpty(yys)) {
                addToBudget(month.getType(), yys, clRow, bjRow, rlRow, sRow, dRow, zzfyRow);
            }
        }
        
        return new ArrayList<>(result.values());
    }
    
    @Override
    public BigDecimal sumBudgetMonth(Date monthDate) {
        BigDecimal total = BigDecimal.ZERO;
        List<CwKrbRow> rows = this.sumByMonth(monthDate);
        if (rows != null) {
            for (CwKrbRow row : rows) {
                if (row != null && row.getYys() != null) {
                    total = total.add(row.getYys());
                }
            }
        }
        return total;
    }
    
    /**
     * 添加到日合计
     */
    private void addToDailyTotal(String type, BigDecimal value, 
                                CwKrbRow clRow, CwKrbRow bjRow, CwKrbRow rlRow, 
                                CwKrbRow sRow, CwKrbRow dRow, CwKrbRow zzfyRow) {
        switch (type) {
            case "cl":
                clRow.setDrs(clRow.getDrs().add(value));
                break;
            case "bj":
                bjRow.setDrs(bjRow.getDrs().add(value));
                break;
            case "rl":
                rlRow.setDrs(rlRow.getDrs().add(value));
                break;
            case "d":
                dRow.setDrs(dRow.getDrs().add(value));
                break;
            case "s":
                sRow.setDrs(sRow.getDrs().add(value));
                break;
            case "zzfy":
                zzfyRow.setDrs(zzfyRow.getDrs().add(value));
                break;
        }
    }
    
    /**
     * 添加到月累计
     */
    private void addToMonthlyTotal(String type, BigDecimal value, 
                                  CwKrbRow clRow, CwKrbRow bjRow, CwKrbRow rlRow, 
                                  CwKrbRow sRow, CwKrbRow dRow, CwKrbRow zzfyRow) {
        switch (type) {
            case "cl":
                clRow.setRlj(clRow.getRlj().add(value));
                break;
            case "bj":
                bjRow.setRlj(bjRow.getRlj().add(value));
                break;
            case "rl":
                rlRow.setRlj(rlRow.getRlj().add(value));
                break;
            case "d":
                dRow.setRlj(dRow.getRlj().add(value));
                break;
            case "s":
                sRow.setRlj(sRow.getRlj().add(value));
                break;
            case "zzfy":
                zzfyRow.setRlj(zzfyRow.getRlj().add(value));
                break;
        }
    }
    
    /**
     * 添加到预算
     */
    private void addToBudget(String type, BigDecimal value,
                            CwKrbRow clRow, CwKrbRow bjRow, CwKrbRow rlRow,
                            CwKrbRow sRow, CwKrbRow dRow, CwKrbRow zzfyRow) {
        switch (type) {
            case "cl":
                clRow.setYys(clRow.getYys().add(value));
                break;
            case "bj":
                bjRow.setYys(bjRow.getYys().add(value));
                break;
            case "rl":
                rlRow.setYys(rlRow.getYys().add(value));
                break;
            case "d":
                dRow.setYys(dRow.getYys().add(value));
                break;
            case "s":
                sRow.setYys(sRow.getYys().add(value));
                break;
            case "zzfy":
                zzfyRow.setYys(zzfyRow.getYys().add(value));
                break;
        }
    }

    @Override
    public CwDsZhcbListResult autoFill(Date queryDate) {
        this.recalculateDrsByDate(queryDate);
        return query(queryDate);
    }

    private List<CwDsDay> autoFillMissingDays(Date beginOfMonth, Date queryDate,
                                              List<CwNameDict> dict,
                                              BigDecimal monthCllyc,
                                              List<CwDsMonth> months) {
        Date endOfDay = DateUtil.endOfDay(queryDate);
        List<CwDsDay> existing = dsDayService.lambdaQuery()
                .between(CwDsDay::getRecordTime, beginOfMonth, endOfDay)
                .list();
        Set<String> existKeys = existing.stream()
                .map(d -> d.getName() + "#" + DateUtil.format(d.getRecordTime(), "yyyy-MM-dd"))
                .collect(Collectors.toSet());

        Map<String, BigDecimal> dsCllMap = getRangeDsCllData(beginOfMonth, endOfDay);

        List<CwDsDay> needInsert = new ArrayList<>();
        Date iter = beginOfMonth;
        while (!iter.after(queryDate)) {
            String dateKey = DateUtil.format(iter, "yyyy-MM-dd");
            BigDecimal dsCll = dsCllMap.get(dateKey);
            if (ObjectUtil.isEmpty(dsCll)) {
                iter = DateUtil.offsetDay(iter, 1);
                continue;
            }
            for (CwNameDict d : dict) {
                String k = d.getName() + "#" + dateKey;
                if (existKeys.contains(k)) {
                    continue;
                }
                CwDsDay newDay = new CwDsDay();
                BeanUtil.copyProperties(d, newDay);
                newDay.resetForCopy();
                newDay.setRecordTime(iter);
                BigDecimal drsVal = calculateDrs(newDay, monthCllyc, dsCll, months);
                newDay.setDrs(drsVal);
                needInsert.add(newDay);
            }
            iter = DateUtil.offsetDay(iter, 1);
        }
        if (!needInsert.isEmpty()) {
            dsDayService.saveBatch(needInsert);
        }
        return needInsert;
    }
}
