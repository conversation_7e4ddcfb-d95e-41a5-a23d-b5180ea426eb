# 月度预算管理条件编辑功能测试说明

## 功能概述
当数据库中存在当前月份的下半月预测数据（second_half_forecast字段不为空）时，系统会自动调整上半月的编辑行为。

## 测试场景

### 场景1：无下半月预测数据（正常情况）
**前置条件：** 当前月份数据库中没有second_half_forecast数据

**测试步骤：**
1. 选择当前月份的1-15号任意日期
2. 打开采矿场/大山厂/泗选厂综合成本表

**预期结果：**
- 表头显示："月预算" + "上半月"标签（绿色）
- 月预算列的输入框可以编辑
- 显示的数值是yys（月预算）字段的值
- 提交时会保存yys数据

### 场景2：存在下半月预测数据（条件编辑）
**前置条件：** 当前月份数据库中存在second_half_forecast数据

**测试步骤：**
1. 选择当前月份的1-15号任意日期
2. 打开采矿场/大山厂/泗选厂综合成本表

**预期结果：**
- 表头显示："月预测" + "下半月"标签（橙色）
- 月预算列的输入框变为只读文本显示
- 显示的数值是yyc（月预测）字段的值，而不是yys
- 提交时不会保存yys数据（被清除）

### 场景3：下半月正常编辑
**测试步骤：**
1. 选择当前月份的16-31号任意日期
2. 打开任意综合成本表

**预期结果：**
- 表头显示："月预测" + "下半月"标签
- 月预测列的输入框可以编辑
- 显示和编辑的是yyc（月预测）字段

## 验证要点

### 1. 界面显示验证
- [ ] 表头标题正确切换（月预算 ↔ 月预测）
- [ ] 状态标签正确显示（上半月 ↔ 下半月）
- [ ] 输入框正确切换（可编辑 ↔ 只读）
- [ ] 数值来源正确（yys ↔ yyc）

### 2. 数据提交验证
- [ ] 无下半月预测时，上半月提交包含yys数据
- [ ] 有下半月预测时，上半月提交不包含yys数据
- [ ] 下半月提交始终包含yyc数据

### 3. 后端数据验证
- [ ] hasSecondHalfForecast字段正确返回
- [ ] second_half_forecast字段存在性检查正确

## 调试信息
系统会在浏览器控制台输出调试信息：
```
采矿场-上半月: hasSecondHalfForecast=true, canEditYys=false
```

## 数据库验证SQL
```sql
-- 检查是否存在下半月预测数据
SELECT name, second_half_forecast 
FROM cw_ckc_month 
WHERE DATE_FORMAT(record_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
  AND second_half_forecast IS NOT NULL;

-- 类似查询适用于 cw_ds_month 和 cw_sx_month 表
```

## 常见问题排查

1. **表头不切换**：检查getMergedColumnTitle()和getYycStatusText()函数
2. **输入框仍可编辑**：检查canEditYys的值和hasSecondHalfForecast状态
3. **显示数值错误**：检查CwRow组件的条件渲染逻辑
4. **提交数据错误**：检查submit函数中的数据清理逻辑
